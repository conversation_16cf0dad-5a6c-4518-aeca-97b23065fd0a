import { Field } from "formik";

const FormikField = ({
  name,
  type = "text",
  options = [],
  onChange,
  multiple = false,
  ...rest
}) => {
  return (
    <Field name={name}>
      {({ form: { values, setFieldValue, errors, touched } }) => {
        const handleChange = (event) => {
          if (type === "file") {
            const file = event.target.files[0] || null;
            setFieldValue(name, file);
            if (onChange) {
              onChange(event);
            }
          } else if (type === "select" && multiple) {
            const { options } = event.target;
            const selectedValues = [];
            for (let i = 0; i < options.length; i++) {
              if (options[i].selected) {
                selectedValues.push(options[i].value);
              }
            }
            setFieldValue(name, selectedValues);
            if (onChange) {
              onChange(event);
            }
          } else {
          const { value } = event.target;
          // Update Formik's state
          setFieldValue(name, value);
          // Call the custom onChange handler if provided
          if (onChange) {
            onChange(event);
          }
        }
        };
        return (
          <>
            {type === "select" ? (
              <select
                name={name}
                value={multiple ? values[name] || [] : values[name]}
                multiple={multiple}
                //onChange={({ target }) => setFieldValue(name, target.value)}
                onChange={handleChange}
                {...rest}
              >
                {!multiple && (
                  <option value="" disabled>
                    Select an option
                  </option>
                )}
                {options.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            ) : type === "textarea" ? (
              <textarea
                name={name}
                value={values[name]}
                onChange={handleChange}
                {...rest}
              />
            ) : type === "file" ? (
              <input
                name={name}
                type="file"
                onChange={handleChange}
                {...rest}
              />
            ): (
              <input
                name={name}
                type={type}
                value={values[name]}
                onChange={({ target }) => setFieldValue(name, target.value)}
                {...rest}
              />
            )}
            {touched[name] && errors[name] && (
              <p className="error">{errors[name]}</p>
            )}
          </>
        );
      }}
    </Field>
  );
};

export default FormikField;
