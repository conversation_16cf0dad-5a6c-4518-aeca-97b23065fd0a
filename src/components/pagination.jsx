import { useState } from "react";

export const PaginationComponent = ({
  totalCount,
  pageSize,
  currentPage,
  setCurrentPage,
  onPageChange,
}) => {
  const totalPages = Math.ceil(totalCount / pageSize);
  const maxVisiblePages = 5; // Change this as needed

  const handlePageChange = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
    onPageChange(page);
  };

  const getPageNumbers = () => {
    let pages = [];
    if (totalPages <= maxVisiblePages) {
      pages = Array.from({ length: totalPages }, (_, i) => i + 1);
    } else {
      if (currentPage <= 3) {
        pages = [1, 2, 3, "...", totalPages];
      } else if (currentPage >= totalPages - 2) {
        pages = [1, "...", totalPages - 2, totalPages - 1, totalPages];
      } else {
        pages = [1, "...", currentPage, "...", totalPages];
      }
    }
    return pages;
  };

  return (
    <ul className="pagination">
      <li className={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
        <a
          className="page-link"
          href="#"
          onClick={() => handlePageChange(currentPage - 1)}
        >
          Previous
        </a>
      </li>

      {getPageNumbers().map((page, index) => (
        <li
          key={index}
          className={`page-item ${currentPage === page ? "active" : ""}`}
        >
          {page === "..." ? (
            <span className="page-link">...</span>
          ) : (
            <a
              className="page-link"
              href="#"
              onClick={() => handlePageChange(page)}
            >
              {page}
            </a>
          )}
        </li>
      ))}

      <li
        className={`page-item ${currentPage === totalPages ? "disabled" : ""}`}
      >
        <a
          className="page-link"
          href="#"
          onClick={() => handlePageChange(currentPage + 1)}
        >
          Next
        </a>
      </li>
    </ul>
  );
};
