import CommonHeader from "../../components/layout/common_header";
import CommonFooter from "../../components/layout/common_footer";
import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { Link, useNavigate } from "react-router-dom";
import {
  useDeleteProductMutation,
  useGetProductListQuery,
  useCreateUpdateVariationsProductsMutation,
  useGetVariationsProductsMutation,
} from "../../feature/api/productDataApiSlice";
import { Table } from "../../components/datatable";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { useMemo, useState } from "react";
import { Modal } from "react-bootstrap";
import { Form } from "react-router-dom";
import { Formik } from "formik";
import FormikField from "../../components/formikField";
import * as yup from "yup";
import WebLoader from "../../components/webLoader";
import { PaginationComponent } from "../../components/pagination";
import { useGetGeneralStatusQuery } from "../../feature/api/statusApiSlice";
import { useListAllCategoriesQuery } from "../../feature/api/categoriesDataApiSlice";
import { useListAllBrandsQuery } from "../../feature/api/brandsDataApiSlice";
import { useGetAllChildCategoriesQuery } from "../../feature/api/childCategoriesDataApiSlice";
import { useGetAllSubCategoriesQuery } from "../../feature/api/subCategoriesDataApiSlice";
import { useListAllAttributesQuery } from "../../feature/api/attributesDataApiSlice";

export default function ProductList() {
  const activePage = "Product Master";
  const linkHref = "/dashboard";

  const navigation = useNavigate();
  /* **************** Start list all Products ******************* */
  const [currentPage, setCurrentPage] = useState(1);
  const [filterCategoryId, setFilterCategoryId] = useState(null);
  const [filterSubCategoryId, setFilterSubCategoryId] = useState(null);
  const [filterChildCategoryId, setFilterChildCategoryId] = useState(null);
  const [filterBrandId, setFilterBrandId] = useState(null);
  const [filterStatus, setFilterStatus] = useState(null);
  const [filterKeywords, setFilterKeywords] = useState("");
  const { data: productListResp, isLoading } = useGetProductListQuery({
      page: currentPage,
      status: parseInt(filterStatus),
      category_id: parseInt(filterCategoryId),
      sub_category_id: parseInt(filterSubCategoryId),
      child_category_id: parseInt(filterChildCategoryId),
      brand_id: parseInt(filterBrandId),
      keywords: filterKeywords,
    });
  const productList = useMemo(() => {
    if (!productListResp?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return productListResp?.data?.list;
  }, [currentPage, productListResp?.data.list]);
  const pageData = useMemo(
    () => productListResp?.data?.page ?? null,
    [productListResp]
  );

  /* **************** End list all Products ******************* */

  /* ****************  Start Filter ****************** */
    const handleStatusFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterStatus(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleCategoryFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterCategoryId(selectedValue);
        handleFilterCategoryChange(event);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleSubCategoryFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterSubCategoryId(selectedValue);
        handleFilterSubCategoryChange(event);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleChildCategoryFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterChildCategoryId(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleBrandFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterBrandId(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleKeywordsFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterKeywords(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };

    /* **************** Start list General Status ******************* */
    const generalStatusData = useGetGeneralStatusQuery();
    const generalStatusDataList = generalStatusData?.data?.data || [];
    const generalStatusList = generalStatusDataList.map((values) => ({
      value: values.id,
      label: values.status,
    }));
    /* **************** End list General Status ******************* */

    /* **************** Start list Brands ******************* */
    const brandData = useListAllBrandsQuery();
    const brandDataList = brandData?.data?.data || [];
    const brandsList = brandDataList.map((values) => ({
      value: values.id,
      label: values.title,
    }));
    /* **************** End list Brands ******************* */
  
    /* **************** Start GET All Categories ******************* */
    const categoriesData = useListAllCategoriesQuery();
    const categoriesDataList = categoriesData?.data?.data || [];
    const categoriesList = categoriesDataList.map((values) => ({
      value: values.id,
      label: values.title,
    }));
    /* **************** End GET All Categories ******************* */

  /* **************** Start Handle category selection change in Filter Select Box ******************* */
  const [selectedFilterCategoryId, setSelectedFilterCategoryId] = useState(null);
  const { data: subCategoriesFilterData } = useGetAllSubCategoriesQuery(
        { category_id: selectedFilterCategoryId },
        { skip: !selectedFilterCategoryId }
      );
  const handleFilterCategoryChange = (e) => {
    const value = e.target.value === "" ? null : parseInt(e.target.value);
    setSelectedFilterCategoryId(value);
  };
  /* **************** End  Handle category selection change in Filter Select Box ******************* */

  /* **************** Start Handle sub category selection change in Filter Select Box ******************* */
  const [selectedFilterSubCategoryId, setSelectedFilterSubCategoryId] = useState(null);
  const { data: childCategoriesFilterData } = useGetAllChildCategoriesQuery(
        { sub_category_id: selectedFilterSubCategoryId },
        { skip: !selectedFilterSubCategoryId }
      );
  const handleFilterSubCategoryChange = (e) => {
    const value = e.target.value === "" ? null : parseInt(e.target.value);
    setSelectedFilterSubCategoryId(value);
  };
  /* **************** End  Handle sub category selection change in Filter Select Box ******************* */

  /* **************** End Filter ***************** */

  /* **************** Start Paginatation ***************** */
  const fetchData = async (page) => {
    console.log(`Fetching data for page ${page}`);
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Paginatation ***************** */

  const [handledeleteProductApi, { isLoading: isDeleteLoading }] =
  useDeleteProductMutation();
  const onDeleteProductHandler = async (id) => {
    try {
      const body = { id: id };
      const resp = await handledeleteProductApi(body).unwrap();
      handleApiSuccess(resp);
      navigation("/products"); // Redirect to the desired page
    } catch (error) {
      handleApiErrors(error);
    }
  };
  
  const onEditProductDetailsHandler = (d) => {
    navigation("/editProduct", { state: d });
  };

  /* **************** Add Attribute Model ******************* */
  const [showAddAttributeModel, setShowAddAttributeModel] = useState(false);
  const handleAddAttributeModelClose = () => setShowAddAttributeModel(false);
  const handleAddAttributeModelShow = () => setShowAddAttributeModel(true);
  /* **************** End Add Attribute Model ******************* */

  /* **************** Start List Attributes List ******************* */
  const attributeResp = useListAllAttributesQuery(
    {skip: (d)showAddAttributeModel}
  );
  const attribute = attributeResp.data?.data || [];
  const attributesAry = attribute.map((attribute) => ({
    value: attribute.id,
    label: attribute.title,
  }));
  /* **************** End List Attributes List ******************* */

  /* **************** Start Add Attributes ******************* */

  const validation = yup.object().shape({
    product_id: yup.number().required().label("Product"),
    attribute_id: yup.array().of(yup.number()).label("Attributes"),
  });

  const [addAttributesValues, setAddAttributeValues] = useState({
    product_id: null,
    attribute_id: [],
  });

  // State for variations data
  const [variationsData, setVariationsData] = useState([]);

  // Get variations products mutation
  const [getVariationsProducts] = useGetVariationsProductsMutation();
    const handleAddAttributeOpenModal = (values) => {
      setAddAttributeValues(values);
      handleAddAttributeModelShow();
    };

    const onAddAttributesHandler = async (d) => {
      try {
        const variationsResponse = await getVariationsProducts({
          product_id: d?.id
        }).unwrap();

        const variations = variationsResponse?.data || [];
        setVariationsData(variations);

        // Get existing attribute IDs from variations and pre-select them in the modal
        const existingAttributeIds = variations.map(variation =>
          variation.attribute_id.toString()
        );

        // Open modal with product data and pre-selected attributes
        handleAddAttributeOpenModal({
          product_id: d?.id || null,
          attribute_id: existingAttributeIds, // Pre-select existing attributes
        });

      } catch (error) {
        console.error("Error fetching variations:", error);
        handleApiErrors(error);
        // Fallback: show all attributes with no pre-selection
        handleAddAttributeOpenModal({
          product_id: d?.id || null,
          attribute_id: [], // No pre-selection on error
        });
      }
    };
    const [handleEditDataApi, { isLoading: isAddAttributesLoading }] =
    useCreateUpdateVariationsProductsMutation();
    const handleAddFormSubmitFunction = async (body) => {
      try {
        const attributeIds = Array.isArray(body.attribute_id)
          ? body.attribute_id.map(id => parseInt(id))
          : [];
        const updatedBody = {
          ...body,
          product_id: parseInt(body.product_id),
          attribute_id: attributeIds,
        };
        const resp = await handleEditDataApi(updatedBody).unwrap();
        handleApiSuccess(resp);
        handleAddAttributeModelClose();
      } catch (error) {
        handleApiErrors(error);
      }
    };

  /* **************** End Add Attributes ******************* */

    /* **************** Web Loader  ******************* */
    if (isLoading || isDeleteLoading || isAddAttributesLoading)
      return <WebLoader />;
    /* **************** End Web Loader  ******************* */

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Product List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <Link
                          type="button"
                          to={"/createProduct"}
                          className="btn btn-primary"
                        >
                          Create New Product
                        </Link>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex  gap-6">
                      <div>
                        <select
                          value={filterCategoryId}
                          className="form-control search-chat py-2 "
                          onChange={handleCategoryFilter}
                        >
                          <option value="">All Categories</option>
                          {categoriesList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterSubCategoryId}
                          className="form-control search-chat py-2 "
                          onChange={handleSubCategoryFilter}
                        >
                          <option value="">All Sub Categories</option>
                          {subCategoriesFilterData?.data?.map((option) => (
                            <option key={option.id} value={option.id}>
                              {option.title}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterChildCategoryId}
                          className="form-control search-chat py-2 "
                          onChange={handleChildCategoryFilter}
                        >
                          <option value="">All Child Categories</option>
                          {childCategoriesFilterData?.data?.map((option) => (
                            <option key={option.id} value={option.id}>
                              {option.title}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterBrandId}
                          className="form-control search-chat py-2 "
                          onChange={handleBrandFilter}
                        >
                          <option value="">All Brands</option>
                          {brandsList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterStatus}
                          className="form-control search-chat py-2 "
                          onChange={handleStatusFilter}
                        >
                          <option value="">All Status</option>
                          {generalStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "title",
                        label: "Product Name",
                        align: "left",
                      },
                      {
                        key: "description",
                        label: "Product Description",
                        align: "left",
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "left",
                      },
                    ]}
                    data={productList}
                    onDeleteHandler={onDeleteProductHandler}
                    onEditHandler={onEditProductDetailsHandler}
                    customActions={[
                      {
                        label: "Add Attributes",
                        icon: "ti ti-plus",
                        handler: onAddAttributesHandler,
                      }
                    ]}
                  />
                  <PaginationComponent
                  totalCount={pageData?.total_count}
                  pageSize={pageData?.page_size}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  onPageChange={fetchData}
                />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

 {/* <!-- Add attribute Modal Start --> */}

      <Modal
        show={showAddAttributeModel}
        onHide={handleAddAttributeModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
      >
        <Modal.Header closeButton>
          <Modal.Title>Add/Update Attribute </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={addAttributesValues}
            validationSchema={validation}
            onSubmit={handleAddFormSubmitFunction}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-create"
                className="needs-validation"
                autoComplete="off"
              >
                <div className="modal-body">
                  <div className="row">
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label
                          id="attribute_id-label"
                          className="form-label"
                        >
                          Select Attributes
                          <span className="un-validation">(*)</span>
                        </label>
                        <div className="border rounded p-3">
                          <FormikField
                            name="attribute_id"
                            id="attribute_id"
                            type="checkbox-group"
                            options={attributesAry}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                    data-bs-dismiss="modal"
                    onClick={handleAddAttributeModelClose}
                  >
                    Close
                  </button>
                  <button
                    className="btn btn-primary"
                    type="submit"
                    onClick={handleSubmit}
                  >
                    Add/Update Attribute
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
      </Modal>
      {/* <!-- Add Atribute Modal end --> */}
      
      <CommonFooter />
    </>
  );
}
