import { useEffect, useMemo, useState } from "react";

import { Formik, Form, Field } from "formik";
import * as yup from "yup";
import FormikField from "../../components/formikField";

import { handleApiErrors } from "../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../hooks/handleApiSucess";

import CommonHeader from "../../components/layout/common_header";
import CommonFooter from "../../components/layout/common_footer";
import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import MapComponent from "../../components/map_component";
import WebLoader from "../../components/webLoader";
import {
  useGetShopInfoQuery,
  useUpdateShopInfoMutation,
} from "../../feature/api/shopDataApiSlice";

const initialValues = {
  shop_name: "",
  shop_name_ar: "",
  shop_phone_code: "",
  shop_phone: "",
  shop_email: "",
  shop_location: "",
  shop_location_ar: "",
  shop_address: "",
  shop_address_ar: "",
};

const validation = yup.object().shape({
  shop_name: yup.string().required().label("Shop Name in English"),
  shop_name_ar: yup.string().required().label("Shop Name in Arabic"),
  shop_phone: yup
    .string()
    .matches(/^\d{8}$/, "Phone number must be exactly 8 digits")
    .required()
    .label("Phone Number"),
  shop_email: yup.string().email().required().label("Email Address"),
  shop_location: yup.string().required().label("Shop location in English"),
  shop_location_ar: yup.string().required().label("Shop location in Arabic"),
  shop_address: yup.string().required().label("Shop address in English"),
  shop_address_ar: yup.string().required().label("Shop address in Arabic"),
});
export default function ShopProfile() {
  const activePage = "Shop Profile";
  const linkHref = "/dashboard";
  const { data, isLoading, isError, error } = useGetShopInfoQuery();
  const [locations, setLocation] = useState({
    lat: 25.4090082,
    lng: 51.5042926,
  });
  useEffect(() => {
    setLocation({
      lat: parseFloat(data?.data?.latitude) || 25.4090082,
      lng: parseFloat(data?.data?.longitude) || 51.5042926,
    });
  }, [data?.data]);

  const shopValues = useMemo(
    () =>
      !data?.data
        ? initialValues
        : {
            shop_name: data.data.shop_name || "",
            shop_name_ar: data.data.shop_name_ar || "",
            shop_phone_code: data.data.shop_phone_code || "",
            shop_phone: data.data.shop_phone || "",
            alternate_shop_phone_code:
              data.data.alternate_shop_phone_code || "",
            alternate_shop_phone: data.data.alternate_shop_phone || "",
            shop_email: data.data.shop_email || "",
            shop_location: data.data.shop_location || "",
            shop_location_ar: data.data.shop_location_ar || "",
            shop_address: data.data.shop_address || "",
            shop_address_ar: data.data.shop_address_ar || "",
          },
    [data?.data]
  );
  useEffect(() => {
    if (!isError) return;
    const errorMessages = handleApiErrors(error);
    console.log(errorMessages);
  }, [isError, error]);

  const [handleUpdateShopApi, { isLoading: isLoading1 }] =
    useUpdateShopInfoMutation();
  const handleSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        latitude: String(locations.lat),
        longitude: String(locations.lng),
      };
      const resp = await handleUpdateShopApi(updatedBody).unwrap();
      const sucessMessages = handleApiSuccess(resp);
      console.log(sucessMessages);
    } catch (error) {
      const errorMessages = handleApiErrors(error);
      console.log(errorMessages);
    }
  };

  if (isLoading || isLoading1) return <WebLoader />;

  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden">
                          <div className="card-body p-4">
                            <h4 className="card-title">Change Shop logo</h4>
                            <p className="card-subtitle mb-4">
                              Change your Shop logo from here
                            </p>
                            <div className="text-center">
                              <img
                                src="/src/assets/images/profile/img_logo.png"
                                alt="matdash-img"
                                className="img-fluid rounded-circle"
                                width="120"
                                height="120"
                              />
                              <div className="d-flex align-items-center justify-content-center my-4 gap-6">
                                <form>
                                  <input
                                    type="file"
                                    className="btn btn-light"
                                  />
                                  <button className="btn btn-primary">
                                    Upload
                                  </button>
                                </form>
                              </div>
                              <p className="mb-0">
                                Allowed JPG, GIF or PNG. Max size of 800K
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Shop Details</h4>
                            <p className="card-subtitle mb-4">
                              To change your personal detail , edit and save
                              from here
                            </p>
                            <Formik
                              initialValues={shopValues}
                              enableReinitialize
                              validationSchema={validation}
                              onSubmit={handleSubmit}
                            >
                              <Form
                                name="profile-update"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="row">
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="shop_name"
                                        className="form-label"
                                      >
                                        Shop Name in English
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="shop_name"
                                        id="shop_name"
                                        placeholder="Shop Name  in English *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="shop_name_ar"
                                        className="form-label"
                                      >
                                        Shop Name in Arabic
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="shop_name_ar"
                                        id="shop_name_ar"
                                        placeholder="Shop Name in Arabic *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label className="form-label">
                                        Shop Phone Number
                                      </label>
                                      <div className="row">
                                        <div className="col-4">
                                          <select
                                            className="form-select"
                                            aria-label="Default select example"
                                            id="shop_phone_code"
                                            name="shop_phone_code"
                                            defaultValue="+974"
                                          >
                                            <option value="">
                                              Choose Phone Code
                                            </option>
                                            <option value="+974">
                                              +974 - Qatar
                                            </option>
                                          </select>
                                        </div>

                                        <div className="col-8">
                                          <FormikField
                                            type="text"
                                            name="shop_phone"
                                            id="shop_phone"
                                            placeholder="Shop Phone Number *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label className="form-label">
                                        Alternate Shop Phone Number
                                      </label>
                                      <div className="row">
                                        <div className="col-4">
                                          <select
                                            className="form-select"
                                            aria-label="Default select example"
                                            id="alternate_shop_phone_code"
                                            name="alternate_shop_phone_code"
                                            defaultValue="+974"
                                          >
                                            <option value="">
                                              Choose Phone Code
                                            </option>
                                            <option value="+974">
                                              +974 - Qatar
                                            </option>
                                          </select>
                                        </div>

                                        <div className="col-8">
                                          <FormikField
                                            type="text"
                                            name="alternate_shop_phone"
                                            id="alternate_shop_phone"
                                            placeholder="Alternate Shop Phone Number "
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="col-lg-12">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="shop_email"
                                        className="form-label"
                                      >
                                        Shop Email Address
                                      </label>
                                      <FormikField
                                        type="email"
                                        name="shop_email"
                                        id="shop_email"
                                        placeholder="Shop Email Address *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="shop_location"
                                        className="form-label"
                                      >
                                        Shop Location in English
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="shop_location"
                                        id="shop_location"
                                        placeholder="Shop Location  in English *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="shop_location_ar"
                                        className="form-label"
                                      >
                                        Shop Location in Arabic
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="shop_location_ar"
                                        id="shop_location_ar"
                                        placeholder="Shop Location  in Arabic *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="shop_address"
                                        className="form-label"
                                      >
                                        Shop Address in English
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="shop_address"
                                        id="shop_address"
                                        placeholder="Shop Address  in English *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="shop_address_ar"
                                        className="form-label"
                                      >
                                        Shop Address in Arabic
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="shop_address_ar"
                                        id="shop_address_ar"
                                        placeholder="Shop Address  in Arabic *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-12">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_address_ar"
                                        className="form-label"
                                      >
                                        Choose Shop Location in Google Map
                                      </label>
                                      <Field>
                                        {({
                                          form: { values, setFieldValue },
                                        }) => {
                                          return (
                                            <MapComponent
                                              location={locations}
                                              setLocation={setLocation}
                                            />
                                          );
                                        }}
                                      </Field>
                                    </div>
                                  </div>
                                </div>
                                <div className="col-12">
                                  <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                    <button
                                      className="btn btn-primary"
                                      type="submit"
                                    >
                                      Update Shop Details
                                    </button>
                                  </div>
                                </div>
                              </Form>
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
