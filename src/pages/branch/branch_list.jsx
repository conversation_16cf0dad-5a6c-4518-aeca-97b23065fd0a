import CommonHeader from "../../components/layout/common_header";
import CommonFooter from "../../components/layout/common_footer";
import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { Link, useNavigate } from "react-router-dom";
import {
  useDeleteShopBranchMutation,
  useGetShopBranchsQuery,
} from "../../feature/api/branchDataApiSlice";
import { Table } from "../../components/datatable";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";

export default function BranchList() {
  const activePage = "Branchs Master";
  const linkHref = "/dashboard";

  // const [DeletePayloadAry, SetDeletePayloadAry] = useState([]);
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const navigation = useNavigate();

  const [handledeleteBranchApi] = useDeleteShopBranchMutation();
  const onDeleteBranchHandler = async (id) => {
    try {
      const body = { branch_id: id };
      const resp = await handledeleteBranchApi(body).unwrap();
      handleApiSuccess(resp);
      navigation("/branch_list"); // Redirect to the desired page
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const onEditBranchDetailsHandler = (d) => {
    console.log(d);
    navigation("/editBranch", { state: d });
  };
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Branch List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <Link
                          type="button"
                          to={"/createBranch"}
                          className="btn btn-primary"
                        >
                          Create New Branch
                        </Link>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                  <Table
                    headCells={[
                      { key: "id", label: "#", align: "left" },
                      {
                        key: "branch_type_name",
                        label: "Branch Type",
                        align: "left",
                      },
                      {
                        key: "branch_name",
                        label: "Branch Name",
                        align: "left",
                      },
                      {
                        key: "branch_ful_phone",
                        label: "Branch Phone Number",
                        align: "left",
                      },
                      {
                        key: "branch_email",
                        label: "Branch Email",
                        align: "left",
                      },
                      {
                        key: "branch_location",
                        label: "Branch Location",
                        align: "left",
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "left",
                      },
                    ]}
                    data={branchList}
                    //onDeleteHandler={(id) => console.log("delete", id)}
                    onDeleteHandler={onDeleteBranchHandler}
                    onEditHandler={onEditBranchDetailsHandler}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
