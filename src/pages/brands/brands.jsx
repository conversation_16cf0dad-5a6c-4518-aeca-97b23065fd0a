import CommonHeader from "../../components/layout/common_header";
import CommonFooter from "../../components/layout/common_footer";
import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { useCreateBrandsMutation, useDeleteBrandsMutation, useEditBrandsMutation, useGetBrandsQuery } from "../../feature/api/brandsDataApiSlice";
import { useMemo, useState } from "react";
import { PaginationComponent } from "../../components/pagination";
import { Table } from "../../components/datatable";
import { Modal } from "react-bootstrap";
import { Form } from "react-router-dom";
import { Formik } from "formik";
import FormikField from "../../components/formikField";
import * as yup from "yup";
import { useGetGeneralStatusQuery } from "../../feature/api/statusApiSlice";
import WebLoader from "../../components/webLoader";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";

const initialValues = {
  title: "",
  title_ar: "",
  is_publish: "",
};

const validation = yup.object().shape({
  title: yup.string().required().label("Brand Name in English"),
  title_ar: yup.string().required().label("Brand Name in Arabic"),
  is_publish: yup.string().required().label("Brand Status"),
});
export default function Brands() {
  const activePage = "Brands";
  const linkHref = "/dashboard";

  /* **************** Data Add Model ******************* */
  const [showAddModel, setShowAddModel] = useState(false);
  const handleAddModelClose = () => setShowAddModel(false);
  const handleAddModelShow = () => setShowAddModel(true);
  /* **************** End Data Add Model ******************* */

  /* **************** Data Edit Model ******************* */
  const [showEditModel, setShowEditModel] = useState(false);
  const handleEditModelClose = () => setShowEditModel(false);
  const handleEditModelShow = () => setShowEditModel(true);
  /* **************** End Data Edit Model ******************* */
  /* **************** Start list all Brands ******************* */
  const [currentPage, setCurrentPage] = useState(1);

  const [filterStatus, setFilterStatus] = useState("");
  const [filterKeywords, setFilterKeywords] = useState("");
  const { data: brandsData, isLoading } = useGetBrandsQuery({
    page: currentPage,
    status: parseInt(filterStatus),
    keywords: filterKeywords,
  });
  const brandsList = useMemo(() => {
    if (!brandsData?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return brandsData?.data?.list;
  }, [currentPage, brandsData?.data.list]);
  const pageData = useMemo(
    () => brandsData?.data?.page ?? null,
    [brandsData]
  );
  /* **************** End list all Brands ******************* */

  /* ****************  Start Filter ****************** */
  const handleStatusFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterStatus(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchData = async (page) => {
    console.log(`Fetching data for page ${page}`);
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** Start list General Status ******************* */
  const generalStatuData = useGetGeneralStatusQuery();
  const generalStatuDataList = generalStatuData?.data?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** Start list General Status ******************* */
  /* **************** Start Create Brands ******************* */
  const [handleCreateDataApi, { isLoading: isCreateLoading }] =
    useCreateBrandsMutation();
  const handleCreateFormSubmitFunction = async (body) => {
    try {
      const updatedBody = {
        ...body,
        is_publish: parseInt(body.is_publish),
      };
      const resp = await handleCreateDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleAddModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create Brands ******************* */
  /* **************** Start Delete Brands ******************* */
  const [handleDeleteDataApi, { isLoading: isDeleteLoading }] =
    useDeleteBrandsMutation();
  const onDeleteDataHandler = async (id) => {
    try {
      const body = { id: id };
      const resp = await handleDeleteDataApi(body).unwrap();
      handleApiSuccess(resp);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Delete Brands ******************* */
  /* **************** Start Edit Brands ******************* */
  const [editValues, setEditValues] = useState({
    id: "",
    title: "",
    title_ar: "",
    is_publish: "",
  });
  const handleOpenModal = (values) => {
    setEditValues(values);
    handleEditModelShow();
  };

  const onEditDataDetailsHandler = (d) => {
    handleOpenModal({
      id: d?.id || "",
      title: d?.title || "",
      title_ar: d?.title_ar || "",
      is_publish: d?.is_publish || "",
    });
  };
  const [handleEditDataApi, { isLoading: isEditLoading }] =
    useEditBrandsMutation();
  const DataUpdateFormSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        id: parseInt(body.id),
        is_publish: parseInt(body.is_publish),
      };
      const resp = await handleEditDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleEditModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Brands ******************* */
  /* **************** Web Loader  ******************* */
  if (isLoading || isCreateLoading || isDeleteLoading || isEditLoading)
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Brands List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <button
                          type="button"
                          className="btn btn-primary"
                          onClick={handleAddModelShow}
                        >
                          Create Brand
                        </button>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                  <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex  gap-6">
                      <div>
                        <select
                          value={filterStatus}
                          className="form-control search-chat py-2 "
                          onChange={handleStatusFilter}
                        >
                          <option value="">All Status</option>
                          {generalStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "title",
                        label: "Brand Name",
                        align: "left",
                      },
                      {
                        key: "title_ar",
                        label: "Brand Name in Arabic",
                        align: "left",
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "left",
                      },
                      {
                        key: "created_at",
                        label: "Created At",
                        align: "left",
                      },
                    ]}
                    data={brandsList}
                    onDeleteHandler={onDeleteDataHandler}
                    onEditHandler={onEditDataDetailsHandler}
                  />
                  <PaginationComponent
                    totalCount={pageData?.total_count}
                    pageSize={pageData?.page_size}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    onPageChange={fetchData}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <!-- Create Modal --> */}

      <Modal
        show={showAddModel}
        onHide={handleAddModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title>Create Brand </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={initialValues}
            validationSchema={validation}
            onSubmit={handleCreateFormSubmitFunction}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-create"
                className="needs-validation"
                autoComplete="off"
              >
                <div className="modal-body">
                  <div className="row">
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title" className="form-label">
                          Brand Name in English{" "}
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title"
                          id="title"
                          placeholder="Brand Name in English"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title_ar" className="form-label">
                          Brand Name in Arabic{" "}
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title_ar"
                          id="title_ar"
                          placeholder="Brand Name in Arabic"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="is_publish" className="form-label">
                          Status
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="is_publish"
                          id="is_publish"
                          className="form-select"
                          type="select"
                          options={generalStatusList}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                    data-bs-dismiss="modal"
                    onClick={handleAddModelClose}
                  >
                    Close
                  </button>
                  <button
                    className="btn btn-primary"
                    type="submit"
                    onClick={handleSubmit}
                  >
                    Create Brand
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
      </Modal>
      {/* <!-- Create Modal end --> */}

      {/* <!-- EditModal --> */}

      <Modal
        show={showEditModel}
        onHide={handleEditModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title> Update Brand</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={editValues}
            enableReinitialize
            validationSchema={validation}
            onSubmit={DataUpdateFormSubmit}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-update"
                className="needs-validation"
                autoComplete="off"
              >
                <FormikField
                  type="hidden"
                  name="role_id"
                  id="role_id"
                  autoComplete="off"
                  className="form-control"
                />
                <div className="modal-body">
                  <div className="row">
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title" className="form-label">
                          Brand Name in English
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title"
                          id="title"
                          placeholder="Brand Name in English"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title_ar" className="form-label">
                          Brand Name in Arabic
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title_ar"
                          id="title_ar"
                          placeholder="Brand Name in Arabic"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="is_publish" className="form-label">
                          Status
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="is_publish"
                          id="is_publish"
                          className="form-select"
                          type="select"
                          options={generalStatusList}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger waves-effect text-start ClassModel"
                    data-bs-dismiss="modal"
                    onClick={handleEditModelClose}
                  >
                    Close
                  </button>
                  <button className="btn btn-primary" onClick={handleSubmit}>
                    Update Brand
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
        {/* <Modal.Footer></Modal.Footer> */}
      </Modal>
      {/* <!-- EditModal end --> */}
      <CommonFooter />
    </>
  );
}
