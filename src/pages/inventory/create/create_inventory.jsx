import CommonHeader from "../../../components/layout/common_header";
import CommonFooter from "../../../components/layout/common_footer";
import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
// import { useGetGeneralStatusQuery } from "../../../feature/api/statusApiSlice";
// import { useListAllBrandsQuery } from "../../../feature/api/brandsDataApiSlice";
import FormikField from "../../../components/formikField";
import {
  useCreateProductMutation,
} from "../../../feature/api/productDataApiSlice";
import { useNavigate } from "react-router-dom";
import { useListAllCategoriesQuery } from "../../../feature/api/categoriesDataApiSlice";
import { useGetAllSubCategoriesQuery } from "../../../feature/api/subCategoriesDataApiSlice";
import { useGetAllChildCategoriesQuery } from "../../../feature/api/childCategoriesDataApiSlice";
import { useMemo, useState } from "react";
// import { useListAllAttributesQuery } from "../../../feature/api/attributesDataApiSlice";
// import { useGetAllAttributesValuesQuery } from "../../../feature/api/attributesValuesDataApiSlice";
import { useGetProductListQuery } from "../../../feature/api/productDataApiSlice";
const initialValues = {
  image: null,
  title: "",
  title_ar: "",
  brand_id: "",
  category_id: "",
  sub_category_id: "",
  child_category_id: "",
  description: "",
  description_ar: "",
  attribute_id: "",
  attribute_value_id: [],
  is_publish: "",
};
const validation = yup.object().shape({
  image: yup.mixed().required().label("Product Image"),
  title: yup.string().required().label("Product Title in English"),
  title_ar: yup.string().required().label("Product Title in Arabic"),
  brand_id: yup.string().required().label("Brand"),
  category_id: yup.string().required().label("Category"),
  sub_category_id: yup.string().label("Sub Category"),
  child_category_id: yup.string().label("Child Category"),
  description: yup.string().required().label("Description in English"),
  description_ar: yup.string().required().label("Description in Arabic"),
  attribute_id: yup.string().required().label("Attribute"),
  attribute_value_id: yup.array().min(1, "At least one attribute value is required").required().label("Attribute Value"),
  is_publish: yup.string().required().label("Status"),
});
export default function CreateInventory() {
  const navigate = useNavigate(); // Initialize the navigation hook
  const activePage = "Inventory";
  const linkHref = "/dashboard";

  /* **************** Start list General Status ******************* */
//   const generalStatuData = useGetGeneralStatusQuery();
//   const generalStatuDataList = generalStatuData?.data?.data || [];
//   const generalStatusList = generalStatuDataList.map((values) => ({
//     value: values.id,
//     label: values.status,
//   }));
  /* **************** End list General Status ******************* */

  /* **************** Start List Brand List ******************* */
//   const brandResp = useListAllBrandsQuery();
//   const brand = brandResp.data?.data || [];
//   const brandAry = brand.map((brand) => ({
//     value: brand.id,
//     label: brand.title,
//   }));

  /* **************** End List Brand List ******************* */

  /* **************** Start Category List ******************* */
  const categoryListResp = useListAllCategoriesQuery();
  const categoryList = categoryListResp.data?.data || [];
  const categoriesList = categoryList.map((values) => ({
    value: values.id,
    label: values.title,
  }));
  /* **************** End Category List ******************* */

  /* **************** Start Fetch Subcategories Based On Category (Load In Select Box) ******************* */
  const [selectedCategoryId, setSelectedCategoryId] = useState(null);
  const { data: subCategoriesData } = useGetAllSubCategoriesQuery(
    { category_id: selectedCategoryId },
    { skip: !selectedCategoryId }
  );
  const subCategoriesList = useMemo(() => {
    if (!subCategoriesData?.data?.length) {
      return [];
    }
    return subCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [subCategoriesData?.data]);
  /* **************** End Fetch Subcategories Based On Category (Load In Select Box) ******************* */
  /* **************** Start Handle category selection change ******************* */
  const handleCategoryChange = (e) => {
    setSelectedCategoryId(parseInt(e.target.value));
  };
  /* **************** End Handle category selection change ******************* */

  /* **************** Start Fetch childCategories Based On SubCategory and  (Load In Select Box) ******************* */
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(null);
  const { data: childCategoriesData } = useGetAllChildCategoriesQuery(
    { sub_category_id: selectedSubCategoryId },
    { skip: !selectedSubCategoryId }
  );
  const childCategoriesList = useMemo(() => {
    if (!childCategoriesData?.data?.length) {
      return [];
    }
    return childCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [childCategoriesData?.data]);
  /* **************** End Fetch childCategories Based On SubCategory (Load In Select Box) ******************* */
  /* **************** Start Handle sub category selection change ******************* */
  const handleSubCategoryChange = (e) => {
    setSelectedSubCategoryId(parseInt(e.target.value));
  };
  /* **************** End Handle sub category selection change ******************* */

   /* **************** Start Handle sub category selection change ******************* */
   const [selectedChildCategoryId, setSelectedChildCategoryId] = useState(null);
   const handleChildCategoryChange = (e) => {
    setSelectedChildCategoryId(parseInt(e.target.value));
  };
  /* **************** End Handle sub category selection change ******************* */

    /* **************** Start Fetch Product Based On Category, SubCategory and   (Load In Select Box) ******************* */
  const { data: productListResp } = useGetProductListQuery({
      category_id: parseInt(selectedCategoryId),
      sub_category_id: parseInt(selectedSubCategoryId),
      child_category_id: parseInt(selectedChildCategoryId)
    });
  const productList = productListResp?.data?.data || [];
  const productAry = productList.map((product) => ({
    value: product.id,
    label: product.title,
  }));

  /* **************** Start List Attributes List ******************* */
//   const attributeResp = useListAllAttributesQuery();
//   const attribute = attributeResp.data?.data || [];
//   const attributeAry = attribute.map((attribute) => ({
//     value: attribute.id,
//     label: attribute.title,
//   }));
  /* **************** End List Attributes List ******************* */


  /* **************** Start Fetch attributeValue Based On Attributes and  (Load In Select Box) ******************* */
//   const [selectedAttributeyId, setSelectedAttributeId] = useState(null);
//   const { data: attributeValueData } = useGetAllAttributesValuesQuery(
//     { attribute_id: selectedAttributeyId },
//     { skip: !selectedAttributeyId }
//   );
//   const attributeValueAry = useMemo(() => {
//     if (!attributeValueData?.data?.length) {
//       return [];
//     }
//     return attributeValueData.data.map((values) => ({
//       value: values.id,
//       label: values.title,
//     }));
//   }, [attributeValueData?.data]);
  /* **************** End Fetch attributeValue Based On Attributes (Load In Select Box) ******************* */
  /* **************** Start Handle Attributes selection change ******************* */
//   const handleAttributeChange = (e) => {
//     setSelectedAttributeId(parseInt(e.target.value));
//   };
  /* **************** End Handle Attributes selection change ******************* */


  // create Product
  const [handleCreateProductApi] = useCreateProductMutation();
  const handleSubmit = async (body) => {
    try {
      const formData = new FormData();
      for (const [key, value] of Object.entries(body)) {
        if (key === "image" && value) {
          formData.append(key, value);
        } else if (key === 'attribute_value_id' && Array.isArray(value)) {
          // Handle multiple attribute values
          value.forEach((val, index) => {
            formData.append(`attribute_value_id[${index}]`, parseInt(val));
          });
        } else if (key === 'is_publish' ||
            key === 'category_id' ||
            key === 'sub_category_id' && value !== "" ||
            key === 'child_category_id' && value !== "" ||
            key === 'brand_id' ||
            key === 'attribute_id') {
          formData.append(key, parseInt(value));
        } else {
          formData.append(key, value);
        }
      }
      const resp = await handleCreateProductApi(formData).unwrap();
      handleApiSuccess(resp);
      navigate("/inventory");
    } catch (error) {
      handleApiErrors(error);
    }
  };
  return (
    <>
      <CommonHeader />
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Inventory Details</h4>
                            <p className="card-subtitle mb-4">
                              To add Inventory, add details and save from here
                            </p>
                            <Formik
                              initialValues={initialValues}
                              validationSchema={validation}
                              onSubmit={handleSubmit}
                            >
                              <Form
                                name="product-create"
                                className="needs-validation"
                                autoComplete="off"
                                encType="multipart/form-data"                      
                              >
                                <div className="row">
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label htmlFor="category_id" className="form-label">
                                        Category
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="category_id"
                                        id="category_id"
                                        className="form-select"
                                        type="select"
                                        options={categoriesList}
                                        onChange={handleCategoryChange}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label htmlFor="sub_category_id" className="form-label">
                                        Sub Category
                                      </label>
                                      <FormikField
                                        name="sub_category_id"
                                        id="sub_category_id"
                                        className="form-select"
                                        type="select"
                                        options={subCategoriesList}
                                        onChange={handleSubCategoryChange}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label htmlFor="child_category_id" className="form-label">
                                        Child Category
                                      </label>
                                      <FormikField
                                        name="child_category_id"
                                        id="child_category_id"
                                        className="form-select"
                                        type="select"
                                        options={childCategoriesList}
                                        onChange = {handleChildCategoryChange}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="is_publish"
                                        className="form-label"
                                      >
                                        Product
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="product_id"
                                        id="product_id"
                                        className="form-select"
                                        type="select"
                                        options={productAry}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Add Inventory
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CommonFooter />
    </>
  );
}
