import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const branchDataApiSlice = createApi({
  reducerPath: "branchDataApiSlice",
  baseQuery,
  tagTypes: ["branchsData"],
  endpoints: (builder) => ({
    getbranchTypes: builder.query({
      query: (body) => ({
        url: `common/branchTypes`,
        method: "GET",
        body,
      }),
    }),
    getShopBranchs: builder.query({
      query: (body) => ({
        url: `branch/getShopBranchs`,
        method: "GET",
        body,
      }),
      providesTags: ["branchsData"],
    }),

    createShopBranch: builder.mutation({
      query: (body) => ({
        url: `branch/createShopBranch`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["branchsData"],
    }),
    getSingleShopBranchs: builder.mutation({
      query: (body) => ({
        url: `branch/getSingleShopBranchs`,
        method: "POST",
        body,
      }),
    }),
    updateShopBranch: builder.mutation({
      query: (body) => ({
        url: `branch/updateShopBranch`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["branchsData"],
    }),

    deleteShopBranch: builder.mutation({
      query: (body) => ({
        url: `branch/deleteShopBranch`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["branchsData"],
    }),
  }),
});

export const {
  useGetShopBranchsQuery,
  useGetbranchTypesQuery,
  useCreateShopBranchMutation,
  useUpdateShopBranchMutation,
  useDeleteShopBranchMutation,
  useGetSingleShopBranchsMutation,
} = branchDataApiSlice;
