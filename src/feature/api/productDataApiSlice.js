import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const productDataApiSlice = createApi({
  reducerPath: "productDataApiSlice",
  baseQuery,
  tagTypes: ["productData"],
  endpoints: (builder) => ({
    getProductList: builder.query({
      query: (body) => ({
        url: `products/listProducts`,
        method: "POST",
        body,
      }),
      providesTags: ["productData"],
    }),
    // listAllProduct: builder.query({
    //   query: () => ({
    //     url: `products/listAllProducts`,
    //     method: "GET",
    //   }),
    //   providesTags: ["productData"],
    // }),
    deleteProduct: builder.mutation({
      query: (body) => ({
        url: `products/deleteProduct`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["productData"],
    }),
    createProduct: builder.mutation({
      query: (body) => ({
        url: `products/createProduct`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["productData"],
    }),
    editProduct: builder.mutation({
      query: (body) => ({
        url: `products/editProduct`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["productData"],
    }),
    singleProduct: builder.mutation({
      query: (body) => ({
        url: `products/singleProduct`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetProductListQuery,
  // useListAllProductQuery,
  useDeleteProductMutation,
  useCreateProductMutation,
  useEditProductMutation,
  useSingleProductMutation,
} = productDataApiSlice;
