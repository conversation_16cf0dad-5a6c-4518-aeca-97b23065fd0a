import { createSlice } from "@reduxjs/toolkit";

const initialState = { token: null, user_id: null, user_type: null };

const authSlice = createSlice({
  name: "authState",
  initialState,
  reducers: {
    setToken(state, action) {
      state.token = action?.payload || null;
    },
    setUserId(state, action) {
      state.user_id = action?.payload || null;
    },
    setUserType(state, action) {
      state.user_type = action?.payload || null;
    },
  },
});

export const { setToken, setUserId, setUserType } = authSlice.actions;
export default authSlice.reducer;
